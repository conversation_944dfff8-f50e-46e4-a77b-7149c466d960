<?php

namespace App\Http\Controllers\Api;

use App\Enums\Trips\TripStatus;
use App\Events\TripEvents\EditTripRequestResponse;
use App\Http\Controllers\Controller;
use App\Http\Responses\ApiResponse;
use App\Models\Trip;
use App\Notifications\Firebase_notifications\NotifyUser;
use App\Services\AreaDetectionService;
use App\Services\GoogleMapsService;
use App\Services\PricingService;
use App\Services\TripPricingService;
use App\Services\TripService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TripPriceController extends Controller
{
    protected $pricingService;

    public function __construct(PricingService $pricingService)
    {
        $this->pricingService = $pricingService;
    }

    public function calculate(Request $request)
    {
        $validated = $request->validate([
            'distance' => 'required|numeric|min:0',
            'area_id' => 'nullable|integer|exists:areas,id',
            'vehicle_type_id' => 'nullable|integer|exists:vehicle_types,id',
            'seat_capacity' => 'nullable|integer|min:1',
            'gender' => 'nullable|string|in:male,female',
            'start_time' => 'required|date',
            'equipment_ids' => 'nullable|array',
            'equipment_ids.*' => 'integer|exists:vehicle_equipment,id',
        ]);

        try {
            $price = $this->pricingService->calculatePrice(
                $validated['distance'],
                $validated['area_id'] ?? null,
                $validated['vehicle_type_id'] ?? null,
                $validated['seat_capacity'] ?? null,
                $validated['gender'] ?? null,
                Carbon::parse($validated['start_time']),
                $validated['equipment_ids'] ?? []
            );

            return response()->json([
                'success' => true,
                'price' => $price,
                'currency' => 'LYD',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function updateTripArrival(
        array $data,
        TripService $tripService,
        TripPricingService $tripPricingService,
        AreaDetectionService $areaDetectionService
    ) {
        try {

            $trip = Trip::findOrFail($data['trip_id']);

            // Verify the trip can be updated
            if ($trip->status->value != (TripStatus::assigned->value ||
            TripStatus::driver_arriving->value ||
            TripStatus::driver_arrived->value ||
            TripStatus::on_trip->value
            )) {
                Log::warning('Trip arrival update rejected - invalid status', [
                    'trip_id' => $data['trip_id'],
                    'current_status' => $trip->status->value,
                ]);

                // broadcast
                return ApiResponse::error(null, 'Cannot edit this trip anymore', 422, null);
            }

            // Get coordinates
            $origin = [
                'lat' => $trip->tripLocation->departure_lat,
                'lng' => $trip->tripLocation->departure_lng,
            ];
            $destination = [
                'lat' => $data['arrival_location']['latitude'],
                'lng' => $data['arrival_location']['longitude'],
            ];

            // Get Google Maps data in a single call
            $googleMapsService = app(GoogleMapsService::class);
            $googleData = $googleMapsService->getCompleteRouteInfo($origin, $destination, false);
            $polyline = $googleData['polyline'];

            // Validate distance
            $distance = isset($googleData['distance_value']) ? $googleData['distance_value'] / 1000 : 0;

            $distanceError = $tripService->validateTripDistance($distance);
            if ($distanceError) {
                return ApiResponse::error(null, $distanceError, 422);
            }

            // Get arrival area ID
            $arrivalAreaId = $areaDetectionService->getAreaIdFromCoordinates(
                $destination['lat'],
                $destination['lng']
            );

            // DB::transaction(function () use ($trip, $validated, $destination, $polyline, $distance, $arrivalAreaId) {
            //     // Update trip location
            //     $trip->tripLocation()->update([
            //         'arrival_address' => $validated['arrival_location']['address'] ?? null,
            //         'arrival_lat' => $destination['lat'],
            //         'arrival_lng' => $destination['lng'],
            //         'polyline' => $polyline,
            //     ]);

            //     // Update trip
            //     $trip->update([
            //         'distance' => $distance,
            //         'arrival_area_id' => $arrivalAreaId,
            //     ]);
            // });

            // Calculate new pricing

            $departureAreaId = $trip->departure_area_id;

            $vehicleTypeIds = [$trip->vehicle->vehicleType->id]; // Only need to calculate for the current vehicle type

            // Determine if female driver is preferred
            $is_female = $trip->rider?->user?->preferences?->driver_gender === 'female';

            // Get equipment IDs
            $vehicle_equipments = optional($trip->vehicleType)->vehicle_equipments ?
            explode(',', $trip->vehicleType->vehicle_equipments) : [];

            // Calculate the pricing
            $allPricingData = PricingService::calculatePrices(
                $googleData,
                $vehicleTypeIds,
                $is_female ? 'female' : null,
                $vehicle_equipments,
                $trip->estimated_departure_time,
                $departureAreaId,
                $trip->estimated_arrival_time,
                $trip->actual_arrival_time
            );

            // Update pricing for current vehicle type
            if (isset($allPricingData[$trip->vehicle_type_id])) {
                $trip->update([
                    'pricing_breakdown' => json_encode($allPricingData[$trip->vehicle_type_id]),
                ]);
            }

            // Process vehicle types for response
            $processedVehicleTypes = $tripPricingService->getProcessedVehicleTypes($vehicleTypeIds, $allPricingData);

            // Extract only the pricing information from the first vehicle type
            $pricingInfo = null;
            if (! empty($processedVehicleTypes) && isset($processedVehicleTypes[0]['pricing'])) {
                $pricingInfo = $processedVehicleTypes[0]['pricing'];
            }

            // Prepare minimal response with only what's needed
            return $responseData = [
                'pricing_breakdown' => $allPricingData,
                'arrival_area_id' => $arrivalAreaId,
                'polyline' => $polyline,
                'estimated_arrival_time' => $trip->estimated_arrival_time ? $trip->estimated_arrival_time : null,
            ];

        } catch (\Exception $e) {
            Log::error('Trip arrival update failed', [
                'trip_id' => $id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ApiResponse::error(null, 'Failed to update trip arrival location: '.$e->getMessage(), 500);
        }
    }

    public function HandleEditTripResponse(array $data)
    {
        $trip = Trip::where('id', $data['trip_id'])->first();
        $editRequest = $trip->editRideRequests()->first();

        if ($data['response'] === 'accept') {

            DB::transaction(function () use ($trip, $editRequest) {
                // Update trip location
                $trip->tripLocation()->update([
                    'arrival_lat' => $editRequest->arrival_lat,
                    'arrival_lng' => $editRequest->arrival_lng,
                    'arrival_address' => $editRequest->arrival_address,
                    'polyline' => $editRequest->polyline,
                ]);

                // Extract the correct pricing structure from new_pricing
                $newPricingData = json_decode($editRequest->new_pricing, true);
                $correctPricingStructure = null;

                if (is_array($newPricingData)) {
                    // Check if it's the nested structure with vehicle_type_id as key
                    if (isset($newPricingData[$trip->vehicle_type_id])) {
                        $correctPricingStructure = $newPricingData[$trip->vehicle_type_id];
                    } elseif (isset($newPricingData['total'])) {
                        // It's already in the correct format
                        $correctPricingStructure = $newPricingData;
                    } else {
                        // Try to get the first pricing entry if it exists
                        $firstKey = array_key_first($newPricingData);
                        if ($firstKey !== null && is_array($newPricingData[$firstKey]) && isset($newPricingData[$firstKey]['total'])) {
                            $correctPricingStructure = $newPricingData[$firstKey];
                        }
                    }
                }

                // Fallback to the original new_pricing if we couldn't extract the correct structure
                $finalPricingBreakdown = $correctPricingStructure ? json_encode($correctPricingStructure) : $editRequest->new_pricing;

                // Update trip details
                $trip->update([
                    'estimated_arrival_time' => $editRequest->estimated_duration,
                    'arrival_area_id' => $editRequest->arrival_area_id,
                    'distance' => $editRequest->distance,
                    'pricing_breakdown' => $finalPricingBreakdown,
                ]);

                // Delete the edit request since it's been accepted
                $editRequest->delete();

                // Broadcast the acceptance
                broadcast(new EditTripRequestResponse($trip, 'accepted'));

                $trip->rider->user->notify(new NotifyUser($trip->rider->user, [
                    'title' => 'تم قبول طلب التعديل',
                    'description' => 'تم قبول طلب تعديل الرحلة بنجاح',
                ]));

                Log::info('Trip update accepted', [
                    'trip_id' => $trip->id,
                    'new_location' => [
                        'lat' => $editRequest->arrival_lat,
                        'lng' => $editRequest->arrival_lng,
                        'address' => $editRequest->arrival_address,
                    ],
                    'original_new_pricing' => json_decode($editRequest->new_pricing, true),
                    'final_pricing_breakdown' => json_decode($finalPricingBreakdown, true),
                    'pricing_structure_corrected' => $correctPricingStructure !== null,
                ]);
            });
        } elseif ($data['response'] === 'reject') {
            Log::info('Trip update rejected', [
                'trip_id' => $trip->id,
                'reason' => $data['reason'] ?? 'No reason provided',
            ]);
            $editRequest->delete();

            broadcast(new EditTripRequestResponse($trip, 'rejected'));

            $trip->rider->user->notify(new NotifyUser($trip->rider->user, [
                'title' => 'تم رفض طلب التعديل',
                'description' => 'تم رفض طلب تعديل الرحلة',
            ]));
        } elseif ($data['response'] === 'timeout') {
            Log::info('Trip update timeouted', [
                'trip_id' => $trip->id,
                'reason' => $data['reason'] ?? 'No reason provided',
            ]);
            $editRequest->delete();

            broadcast(new EditTripRequestResponse($trip, 'timeouted'));

            $trip->rider->user->notify(new NotifyUser($trip->rider->user, [
                'title' => 'لم يتم الرد على طلب تعديل الرحلة',
                'description' => 'لم يقم السائق بالرد على طلب التعديل. يمكنك تعديل الرحلة من جديد أو الاستمرار بها دون تغيير',
            ]));
        }
    }
}
