<?php

namespace App\Http\Controllers\Api;

use App\Enums\GenderEnum;
use App\Enums\Trips\CancellationStage;
use App\Enums\Trips\TripStatus;
use App\Enums\UserStatus;
use App\Events\TripEvents\SearchResponse;
use App\Events\TripEvents\TripTimeoutOccurred;
use App\Http\Controllers\Controller;
use App\Http\Requests\TripsRequests\TripStoreRequest;
use App\Http\Requests\TripsRequests\TripUpdateRequest;
use App\Http\Resources\TripResource;
use App\Http\Responses\ApiResponse;
use App\Jobs\SearchAvailableDrivers;
use App\Models\Area;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripRefusedDriver;
use App\Models\VehicleType;
use App\Services\AreaDetectionService;
use App\Services\DriverRequestService;
use App\Services\GoogleMapsService;
use App\Services\PricingService;
use App\Services\TripPricingService;
use App\Services\TripService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TripController extends Controller
{
    protected $driverRequestService;

    public function __construct(DriverRequestService $driverRequestService)
    {
        $this->driverRequestService = $driverRequestService;
    }

    /**
     * Create a new trip
     *
     * @response array{data : TripResource ,extra : object, message: string, status: integer,error : string}
     */
    public function store(
        TripStoreRequest $request,
        TripService $tripService,
        TripPricingService $tripPricingService,
        AreaDetectionService $areaDetectionService
    ): ApiResponse|array {
        try {
            $validated = $request->validated();
            $user = Auth::user();
            $rider = $user->rider;

            if (! $rider) {
                return ApiResponse::error(null, 'Must be an authenticated rider', 403);
            }

            // Check if is_female is set in the request, otherwise use user preferences
            $is_female = isset($validated['is_female']) ? (bool) $validated['is_female'] :
                        (isset($user->preferences->driver_gender) &&
                         $user->preferences->driver_gender === 'female');

            $departureAreaId = $areaDetectionService->getAreaIdFromCoordinates(
                $validated['departure_location']['latitude'],
                $validated['departure_location']['longitude']
            );

            $arrivalAreaId = $areaDetectionService->getAreaIdFromCoordinates(
                $validated['arrival_location']['latitude'],
                $validated['arrival_location']['longitude']
            );

            $validated['departure_area_id'] = $departureAreaId;
            $validated['arrival_area_id'] = $arrivalAreaId;

            $origin = [
                'lat' => $validated['departure_location']['latitude'],
                'lng' => $validated['departure_location']['longitude'],
            ];
            $destination = [
                'lat' => $validated['arrival_location']['latitude'],
                'lng' => $validated['arrival_location']['longitude'],
            ];

            $googleMapsService = app(GoogleMapsService::class);
            // Use getCompleteRouteInfo to get distance, time, and polyline in a single API call
            $googleData = $googleMapsService->getCompleteRouteInfo($origin, $destination, false);
            if (! isset($googleData['distance_value'])) {
                return ApiResponse::error(null, 'Failed to calculate trip distance. Please try again.', 422);
            }

            $validated['polyline'] = $googleData['polyline'];

            $distance = $googleData['distance_value'] / 1000;
            $distanceError = $tripService->validateTripDistance($distance);
            if ($distanceError) {
                return ApiResponse::error(null, $distanceError, 422);
            }

            $trip = $tripService->createTrip($validated, $rider->id, $is_female);

            if (! $trip) {
                return ApiResponse::error(null, 'Something went wrong while creating new trip', 403);
            }

            $vehicleTypeIds = $tripPricingService->getValidVehicleTypeIds($validated);
            $genderParam = $is_female ? 'female' : 'male';

            $allPricingData = PricingService::calculatePrices(
                $googleData,
                $vehicleTypeIds,
                $genderParam,
                $validated['vehicle_equipments'] ?? [],
                $trip->estimated_departure_time,
                $departureAreaId,
                $trip->estimated_arrival_time,
                $trip->actual_arrival_time
            );

            $processedVehicleTypes = $tripPricingService->getProcessedVehicleTypes($vehicleTypeIds, $allPricingData);

            return ApiResponse::successExtraPayload(
                new TripResource($trip),
                $processedVehicleTypes,
                'Trip created successfully',
                201
            );

        } catch (\Exception $e) {
            return ApiResponse::error(null, 'Failed to create trip: '.$e->getMessage(), 500);
        }
    }

    /**
     * Update a dispatched trip
     *
     * @response array{data : TripResource ,extra : object, message: string, status: integer,error : string}
     */
    public function update(
        TripUpdateRequest $request,
        string $id,
        TripService $tripService,
        TripPricingService $tripPricingService,
        AreaDetectionService $areaDetectionService
    ): ApiResponse|array {
        return DB::transaction(function () use ($request, $id, $tripService, $tripPricingService, $areaDetectionService) {
            try {
                Log::info('Starting trip update process', ['trip_id' => $id]);

                // Lock the trip record first to prevent concurrent modifications
                $trip = Trip::where('id', $id)->lockForUpdate()->firstOrFail();
                $user = Auth::user();
                $validated = $request->validated();

                // Check if trip is already assigned - if so, don't allow updates
                if ($trip->status->value === TripStatus::assigned->value) {
                    Log::warning('Trip update rejected - trip already assigned', [
                        'trip_id' => $id,
                        'current_status' => $trip->status->value,
                    ]);

                    return ApiResponse::error(null, 'Cannot edit trip that has already been assigned to a driver', 422, null);
                }

                // Check if trip is still in dispatched state
                if ($trip->status->value != TripStatus::dispatched->value) {
                    Log::warning('Trip update rejected - invalid status', [
                        'trip_id' => $id,
                        'current_status' => $trip->status->value,
                    ]);

                    return ApiResponse::error(null, 'Cannot edit trip that is not in dispatched state', 422, null);
                }

                // Set a flag in cache to force-terminate any ongoing search
                Cache::put("trip_{$id}_force_terminate", true, 300);

                // Get the list of contacted drivers for this trip

                $origin = [
                    'lat' => $validated['departure_location']['latitude'] ?? $trip->tripLocation->departure_lat,
                    'lng' => $validated['departure_location']['longitude'] ?? $trip->tripLocation->departure_lng,
                ];
                $destination = [
                    'lat' => $validated['arrival_location']['latitude'] ?? $trip->tripLocation->arrival_lat,
                    'lng' => $validated['arrival_location']['longitude'] ?? $trip->tripLocation->arrival_lng,
                ];

                // Get Google Maps data in a single call
                $googleMapsService = app(GoogleMapsService::class);
                $googleData = $googleMapsService->getCompleteRouteInfo($origin, $destination, false);
                $polyline = $googleData['polyline'];

                // Validate distance
                $distance = isset($googleData['distance_value']) ? $googleData['distance_value'] / 1000 : 0;

                $distanceError = $tripService->validateTripDistance($distance);
                if ($distanceError) {
                    return ApiResponse::error(null, $distanceError, 422);
                }

                $contactedDrivers = $trip->contacted_drivers ?? [];

                // Release driver locks and clear queued requests
                if (! empty($contactedDrivers)) {
                    Log::info('Releasing locks for contacted drivers during update', [
                        'trip_id' => $trip->id,
                        'driver_count' => count($contactedDrivers),
                    ]);

                    $driverRequestService = app(DriverRequestService::class);
                    foreach ($contactedDrivers as $driverId) {
                        $driverRequestService->releaseDriverLock($driverId);

                        // Clear any queued requests
                        $queueKey = "driver_request_queue_{$driverId}";
                        $queue = Cache::get($queueKey, []);
                        if (! empty($queue)) {
                            $newQueue = array_filter($queue, function ($request) use ($id) {
                                return $request['trip_id'] != $id;
                            });
                            Cache::put($queueKey, $newQueue, 300);
                        }

                        // Notify driver that the trip was updated/cancelled
                        broadcast(new TripTimeoutOccurred($trip, 'trip_updated_by_rider', $driverId));
                    }
                }

                // Get coordinates

                // Change trip status to pending to prevent driver assignment during update
                $trip->update([
                    'status' => TripStatus::pending->value,
                    'contacted_drivers' => [],
                ]);

                // Clear any refused drivers for this trip to give them another chance
                TripRefusedDriver::where('trip_id', $id)->delete();
                // Get area IDs
                $departureAreaId = $areaDetectionService->getAreaIdFromCoordinates(
                    $origin['lat'],
                    $origin['lng']
                );
                $arrivalAreaId = $areaDetectionService->getAreaIdFromCoordinates(
                    $destination['lat'],
                    $destination['lng']
                );

                // Update trip location - store new polyline separately
                try {
                    // Start a database transaction
                    DB::beginTransaction();

                    // Lock the related trip record first
                    $lockedTrip = Trip::where('id', $id)->lockForUpdate()->first();
                    if (! $lockedTrip) {
                        throw new \Exception('Trip not found or could not be locked');
                    }

                    // Now update the trip location
                    $trip->tripLocation()->update([
                        'departure_address' => $validated['departure_location']['address'] ?? null,
                        'arrival_address' => $validated['arrival_location']['address'] ?? null,
                        'departure_lat' => $origin['lat'],
                        'departure_lng' => $origin['lng'],
                        'arrival_lat' => $destination['lat'],
                        'arrival_lng' => $destination['lng'],
                        'new_polyline' => $polyline, // Store in new column instead of polyline
                    ]);

                    // Commit the transaction
                    DB::commit();

                    Log::info('Trip location updated successfully with new_polyline', ['trip_id' => $id]);
                } catch (\Exception $e) {
                    // Rollback the transaction in case of error
                    DB::rollBack();

                    Log::error('Failed to update trip location', [
                        'trip_id' => $id,
                        'error' => $e->getMessage(),
                    ]);
                    throw $e;
                }

                // Update trip
                try {
                    $trip->update([
                        'distance' => $distance,
                        'departure_area_id' => $departureAreaId,
                        'arrival_area_id' => $arrivalAreaId,
                    ]);
                    Log::info('Trip details updated successfully', ['trip_id' => $id]);
                } catch (\Exception $e) {
                    Log::error('Failed to update trip details', [
                        'trip_id' => $id,
                        'error' => $e->getMessage(),
                    ]);
                    throw $e;
                }

                // Get valid vehicle types
                $vehicleTypeIds = $tripPricingService->getValidVehicleTypeIds([
                    'departure_area_id' => $departureAreaId,
                    'arrival_area_id' => $arrivalAreaId,
                    'vehicle_category' => $trip->vehicleType->vehicle_category,
                ]);

                // Check female preference
                $is_female = false;
                if ($trip->rider?->user?->preferences?->driver_gender === 'female') {
                    $is_female = true;
                }

                $vehicle_equipments = $trip->vehicleType->vehicle_equipments ? explode(',', $trip->vehicleType->vehicle_equipments) : [];

                // calculate the pricing
                // Always pass a gender value (male or female) to ensure consistent pricing
                $genderParam = $is_female ? 'female' : 'male';

                // Log the gender parameter
                Log::info('Gender parameter for pricing calculation in update', [
                    'is_female' => $is_female,
                    'gender_param' => $genderParam,
                ]);

                $allPricingData = PricingService::calculatePrices(
                    $googleData,
                    $vehicleTypeIds,
                    $genderParam,
                    $vehicle_equipments ?? [],
                    $trip->estimated_departure_time,
                    $departureAreaId,
                    $trip->estimated_arrival_time,
                    $trip->actual_arrival_time
                );
                // Process vehicle types
                $processedVehicleTypes = $tripPricingService->getProcessedVehicleTypes($vehicleTypeIds, $allPricingData);

                // Prepare response
                $responseData = [
                    'distance' => $distance,
                    'polyline' => $polyline, // Send the new polyline to display
                    'google_data' => $googleData,
                    'origin' => $origin,
                    'destination' => $destination,
                    'departure_area_id' => $departureAreaId,
                    'arrival_area_id' => $arrivalAreaId,
                ];

                TripRefusedDriver::where('trip_id', $trip->id)->delete();

                return ApiResponse::successExtraPayload(
                    $responseData,
                    $processedVehicleTypes,
                    'Trip location updated successfully',
                    200
                );

            } catch (\Exception $e) {
                Log::error('Trip update failed', [
                    'trip_id' => $id ?? null,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'line' => $e->getLine(),
                    'file' => $e->getFile(),
                ]);

                throw $e; // Re-throw to trigger transaction rollback
            }
        }, 5); // 5 retries for deadlock
    }

    /**
     * Cancel the search for nearby drivers
     *
     * @response array{data : null , message: 'trip search cancelled successfully', status: 200,error : string}
     */
    public function cancelDriverSearch(Request $request, int $id)
    {
        $trip = Trip::where('id', $id)->first();

        if (! Auth::user()->rider()->exists() || $trip->rider_id !== Auth::user()->rider->id) {
            return ApiResponse::error(null, 'Unauthorized Access', 401, null);
        }

        // Use a transaction with pessimistic locking to prevent race conditions
        return DB::transaction(function () use ($trip, $id) {
            // Lock the trip record to prevent concurrent modifications
            $trip = Trip::where('id', $id)->lockForUpdate()->first();

            if (! $trip) {
                return ApiResponse::error(null, 'Trip not found', 404, null);
            }

            if ($trip->status->value === TripStatus::dispatched->value || $trip->status->value === TripStatus::assigned->value) {
                // Get the list of contacted drivers for this trip
                $contactedDrivers = $trip->contacted_drivers ?? [];

                // Release driver locks
                if (! empty($contactedDrivers)) {
                    Log::info('Releasing locks for contacted drivers', [
                        'trip_id' => $trip->id,
                        'driver_count' => count($contactedDrivers),
                    ]);

                    $driverRequestService = app(DriverRequestService::class);
                    foreach ($contactedDrivers as $driverId) {
                        $driverRequestService->releaseDriverLock($driverId);
                    }
                }

                // Update trip status immediately to prevent other operations
                $trip->update([
                    'status' => TripStatus::canceled->value,
                    'cancelled_by' => 'rider',
                    'cancellation_stage' => CancellationStage::afterDispatch->value,
                ]);

                // If the trip has a driver, update their status to online in the database
                if ($trip->driver_id) {
                    Log::info('Updating driver and rider status to online', [
                        'trip_id' => $trip->id,
                        'driver_id' => $trip->driver_id,
                        'rider_id' => $trip->rider_id,
                    ]);

                    // Update driver status in database
                    $trip->driver->user->update(['status' => UserStatus::ONLINE->value]);
                    $trip->rider->user->update(['status' => UserStatus::ONLINE->value]);
                }

                foreach ($contactedDrivers as $driverId) {
                    $queueKey = "driver_request_queue_{$driverId}";
                    $queue = Cache::get($queueKey, []);

                    if (! empty($queue)) {
                        // Remove any queued requests for this trip
                        $newQueue = array_filter($queue, function ($request) use ($id) {
                            return $request['trip_id'] != $id;
                        });

                        if (count($newQueue) !== count($queue)) {
                            Cache::put($queueKey, $newQueue, 300);
                            Log::info('Removed queued requests for cancelled trip', [
                                'driver_id' => $driverId,
                                'trip_id' => $id,
                                'removed_count' => count($queue) - count($newQueue),
                            ]);
                        }
                    }

                    // Send cancellation to driver
                    broadcast(new TripTimeoutOccurred($trip, 'trip_cancelled_by_rider', $driverId));
                }

                return ApiResponse::success(null, 'trip search cancelled successfully', 201, null);
            } else {
                return ApiResponse::error(null, 'Cannot cancel trip is not dispatched anymore', 422, null);
            }

        }, 5); // 5 retries for deadlock
    }

    /**
     * Confirm a trip and start searching for drivers
     *
     * @param  int  $tripId
     *
     * @response array{data : null , message: 'trip confirmed successfully', status: 200,error : string}
     */
    public function confirmTrip(Request $request, $tripId, TripPricingService $tripPricingService)
    {
        $validated = $request->validate([
            'vehicle_type_id' => 'required|numeric',
            'lng' => 'required|numeric',
            'lat' => 'required|numeric',
            'vehicle_category' => 'required|string',
            'rider_id' => 'required|numeric',
        ]);

        try {
            // Use a transaction with locking to prevent race conditions
            return DB::transaction(function () use ($validated, $tripId) {
                // Clear any force termination flag to ensure a clean start
                Cache::forget("trip_{$tripId}_force_terminate");

                // Lock the trip record to prevent concurrent modifications
                $trip = Trip::where('id', $tripId)->lockForUpdate()->firstOrFail();

                // Verify trip is in pending state (after update)
                if ($trip->status->value !== TripStatus::pending->value) {
                    Log::warning('Trip confirmation rejected - invalid status', [
                        'trip_id' => $tripId,
                        'current_status' => $trip->status->value,
                    ]);

                    return ApiResponse::error(null, 'Cannot confirm trip that is not in pending state', 422);
                }

                // Apply the new_polyline if it exists
                if ($trip->tripLocation && $trip->tripLocation->new_polyline) {
                    $trip->tripLocation()->update([
                        'polyline' => $trip->tripLocation->new_polyline,
                        'new_polyline' => null, // Clear the temporary value
                    ]);

                    Log::info('Applied new polyline during trip confirmation', [
                        'trip_id' => $tripId,
                    ]);
                }

                // Ensure contacted_drivers is empty before starting a new search
                if (! empty($trip->contacted_drivers)) {
                    $trip->update(['contacted_drivers' => []]);
                }

                // Get the pricing data for the selected vehicle type
                $googleMapsService = app(GoogleMapsService::class);
                $origin = ['lat' => $trip->tripLocation->departure_lat, 'lng' => $trip->tripLocation->departure_lng];
                $destination = ['lat' => $trip->tripLocation->arrival_lat, 'lng' => $trip->tripLocation->arrival_lng];
                $googleData = $googleMapsService->getDistanceAndTime($origin, $destination, true); // Explicit traffic=true for pricing

                // Safely check for female preference
                $is_female = false;
                if ($trip->rider &&
                    $trip->rider->user &&
                    $trip->rider->user->preferences &&
                    isset($trip->rider->user->preferences->driver_gender)) {
                    $is_female = $trip->rider->user->preferences->driver_gender === 'female';
                }

                // Always pass a gender value (male or female) to ensure consistent pricing
                $genderParam = $is_female ? 'female' : 'male';

                // Get equipment IDs from the trip's vehicle type
                $vehicle_equipments = $trip->vehicleType->vehicle_equipments ?
                    explode(',', $trip->vehicleType->vehicle_equipments) : [];

                $pricingData = PricingService::calculatePrices(
                    $googleData,
                    [$validated['vehicle_type_id']], // Only calculate for selected vehicle type
                    $genderParam,
                    $vehicle_equipments,
                    $trip->estimated_departure_time,
                    $trip->departure_area_id,
                    $trip->estimated_arrival_time,
                    $trip->actual_arrival_time
                );

                $filteredPricingData = $pricingData[$validated['vehicle_type_id']] ?? null;

                if (! $filteredPricingData) {
                    return ApiResponse::error(null, 'Invalid vehicle type or pricing calculation failed', 422);
                }

                // Update trip status to dispatched
                $trip->update([
                    'pricing_breakdown' => json_encode($filteredPricingData),
                    'status' => TripStatus::dispatched->value,
                ]);

                $trip->rider->update(['last_heartbeat' => now()]);
                $eventData = [
                    'trip_id' => $tripId,
                    'lng' => $validated['lng'],
                    'lat' => $validated['lat'],
                    'vehicle_category' => $validated['vehicle_category'],
                    'rider_id' => $validated['rider_id'],
                    'vehicle_type_id' => $validated['vehicle_type_id'],
                ];

                Log::info('Dispatching search event', ['event_data' => $eventData]);
                SearchAvailableDrivers::dispatch($eventData, $tripId);

                return new ApiResponse(null, 'Trip confirmed successfully', 200);
            }, 5); // 5 retries for deadlock
        } catch (\Exception $e) {
            Log::error('Error during trip confirmation', [
                'error' => $e->getMessage(),
                'trip_id' => $tripId,
            ]);

            return ApiResponse::error(null, 'Error confirming trip: '.$e->getMessage(), 500);
        }
    }

    // pending until we found available driver
    public function getAvailableDrivers($validated, $tripId)
    {
        // Add force termination check at the beginning
        if (Cache::get("trip_{$tripId}_force_terminate", false)) {
            Log::info('Search force-terminated - Trip was updated', [
                'trip_id' => $tripId,
            ]);
            Cache::forget("trip_{$tripId}_force_terminate");

            return;
        }

        // Add status check at the beginning
        $trip = Trip::where('id', $tripId)->first();

        if (! $trip || $trip->status->value !== TripStatus::dispatched->value) {
            Log::info('Search stopped - Trip no longer dispatched', [
                'trip_id' => $tripId,
                'status' => $trip ? $trip->status->value : 'not_found',
            ]);

            return;
        }

        $searchRadius = [
            ['from' => env('DRIVER_SEARSH_FIRST_RADIUS_FROM'), 'to' => env('DRIVER_SEARSH_FIRST_RADIUS_TO'), 'timeout' => env('FIRST_RADIUS_TIMEOUT')],
            ['from' => env('DRIVER_SEARSH_SECONDE_RADIUS_FROM'), 'to' => env('DRIVER_SEARSH_SECONDE_RADIUS_TO'), 'timeout' => env('SECONDE_RADIUS_TIMEOUT')],
            ['from' => env('DRIVER_SEARSH_THIRD_RADIUS_FROM'), 'to' => env('DRIVER_SEARSH_THIRD_RADIUS_TO'), 'timeout' => env('THIRD_RADIUS_TIMEOUT')],
        ];

        $rider = Rider::with(relations: 'user')->find($validated['rider_id']);
        $vehicle_type = VehicleType::find($validated['vehicle_type_id'])->name_en;
        $radiusContactedDrivers = [];
        $allContactedDrivers = [];
        $searchCompleted = false;

        foreach ($searchRadius as $tier) {
            // Add force termination check before each radius tier
            if (Cache::get("trip_{$tripId}_force_terminate", false)) {
                Log::info('Search force-terminated during radius tier - Trip was updated', [
                    'trip_id' => $tripId,
                ]);
                Cache::forget("trip_{$tripId}_force_terminate");

                return;
            }

            // Add status check before each iteration
            $trip = Trip::where('id', $tripId)->first();
            if (! $trip || $trip->status->value !== TripStatus::dispatched->value) {
                Log::info('Search stopped during radius tier - Trip no longer dispatched', [
                    'trip_id' => $tripId,
                    'status' => $trip ? $trip->status->value : 'not_found',
                ]);

                return;
            }

            Log::info('Starting search in radius tier', [
                'from' => $tier['from'],
                'to' => $tier['to'],
                'timeout' => $tier['timeout'],
            ]);

            $startTime = time();
            $timeoutSeconds = (int) $tier['timeout'];

            while (time() - $startTime < $timeoutSeconds) {
                // Add force termination check in the inner loop
                if (Cache::get("trip_{$tripId}_force_terminate", false)) {
                    Log::info('Search force-terminated in inner loop - Trip was updated', [
                        'trip_id' => $tripId,
                    ]);
                    Cache::forget("trip_{$tripId}_force_terminate");

                    return;
                }

                $trip = Trip::where('id', $tripId)->first();

                // Check trip status
                $activeStatuses = [
                    TripStatus::assigned->value,
                    TripStatus::driver_arriving->value,
                    TripStatus::driver_arrived->value,
                    TripStatus::on_trip->value,
                    TripStatus::waiting_for_driver_confirmation->value,
                ];

                if (
                    ! $trip ||
                    $trip->status->value === TripStatus::canceled->value ||
                    in_array($trip->status->value, $activeStatuses)
                ) {
                    $status = $trip ? $trip->status->value : 'not_found';
                    $isDriverFound = in_array($status, $activeStatuses);

                    $message = $isDriverFound
                        ? 'Driver found for trip'
                        : 'Search cancelled - Trip cancelled or not found';

                    Log::info($message, ['trip_id' => $tripId, 'status' => $status]);

                    return ApiResponse::success(
                        ['status' => $isDriverFound ? 'driver_found' : 'search_cancelled'],
                        $isDriverFound ? 'Driver Found' : 'Search cancelled',
                        200
                    );
                }

                // Search for drivers
                $refusedDrivers = $trip->refusedDrivers->pluck('id')->toArray() ?? [];
                $driverIds = $this->nearBydrivers($validated['lat'], $validated['lng'], (int) $tier['to'] * 1000);
                Log::info('nearby drivers : ', [$driverIds]);

                if (empty($driverIds)) {
                    Log::info('No drivers found in current radius', [
                        'radius' => $tier['to'],
                        'lat' => $validated['lat'],
                        'lng' => $validated['lng'],
                    ]);
                    sleep(3);

                    continue;
                }

                // Safely check for female preference
                $female_only = false;
                if ($trip->rider &&
                    $trip->rider->user &&
                    $trip->rider->user->preferences &&
                    isset($trip->rider->user->preferences->driver_gender)) {
                    $female_only = $trip->rider->user->preferences->driver_gender === 'female';
                }

                $validDrivers = $this->getValidDrivers($driverIds, $validated['vehicle_category'], $vehicle_type, $female_only);
                Log::info('valid drivers : ', [$validDrivers]);

                $eligibleDrivers = $this->filterValidDrivers($validDrivers, $validated, $tripId);
                Log::info('eligibleDrivers drivers : ', [$eligibleDrivers]);

                $availableDrivers = array_diff($eligibleDrivers, $refusedDrivers);

                if (! empty($availableDrivers)) {
                    Log::info('Available drivers found', [
                        'trip_id' => $tripId,
                        'drivers' => $availableDrivers,
                    ]);

                    foreach ($availableDrivers as $driverId) {
                        // Double-check trip status before sending request
                        $currentTrip = Trip::find($tripId);
                        if (! $currentTrip || $currentTrip->status->value !== TripStatus::dispatched->value) {
                            Log::info('Aborting driver request - trip no longer dispatched', [
                                'trip_id' => $tripId,
                                'status' => $currentTrip ? $currentTrip->status->value : 'not_found',
                            ]);
                            break; // Exit the loop entirely
                        }

                        $tripDetails = [
                            'rider' => $rider,
                            'pricing' => json_decode($trip->pricing_breakdown, true),
                            'trip_location' => $trip->tripLocation,
                            'rider_notes' => $trip->rider_notes,
                            'distance' => $trip->distance,
                        ];

                        // Try to send request with locking
                        Log::info('Sending request to driver', [
                            'driver_id' => $driverId,
                            'trip_id' => $tripId,
                        ]);
                        $sent = $this->driverRequestService->sendRequest($driverId, $tripDetails, $tripId);

                        if ($sent) {
                            // Request was sent successfully
                            $radiusContactedDrivers[] = $driverId;
                            $allContactedDrivers[] = $driverId;

                            // Update the contacted_drivers field in the trip
                            $currentContacted = $trip->contacted_drivers ?? [];
                            if (! in_array($driverId, $currentContacted)) {
                                $currentContacted[] = $driverId;
                                $trip->update(['contacted_drivers' => $currentContacted]);
                            }

                            $searchCompleted = true;
                        } else {
                            // Driver already has an active request - skip and try next driver
                            Log::info('Skipping driver with active request', [
                                'driver_id' => $driverId,
                                'trip_id' => $tripId,
                            ]);
                        }
                        // }
                    }
                }

                sleep(3);
            }

            Log::info('Expanding search radius', [
                'completed_tier' => $tier['to'],
                'drivers_contacted' => count($radiusContactedDrivers),
            ]);

            if ((int) $tier['timeout'] === 60) {
                broadcast(new SearchResponse($trip, 'Expanding-search-radius'));
                Log::info('Broadcast expanding search radius message completed');
            }
            $radiusContactedDrivers = [];
        }

        // Final status check
        $finalTrip = Trip::where('id', $tripId)->first();

        if ($finalTrip && $finalTrip->status->value === TripStatus::dispatched->value) {
            // Case 1: No drivers found at all
            if (! $searchCompleted || empty($allContactedDrivers)) {
                $finalTrip->update(['status' => TripStatus::timeout->value]);
                Log::info('Search timeout - No drivers found in any radius');
                broadcast(new SearchResponse($finalTrip, 'timeout'));

                return ApiResponse::success(['status' => 'timeout'], 'No drivers found', 200);
            }

            // Case 2: Check if all contacted drivers refused
            $refusedDrivers = $finalTrip->refusedDrivers->pluck('id')->toArray();
            Log::info('refusedDrivers', ['refusedDrivers' => $refusedDrivers]);
            $allDriversRefused = ! empty($allContactedDrivers) &&
                count(array_intersect($allContactedDrivers, $refusedDrivers)) === count($allContactedDrivers);

            if ($allDriversRefused) {
                $finalTrip->update(['status' => TripStatus::rejected->value]);
                Log::info('All contacted drivers refused the trip');
                broadcast(new SearchResponse($finalTrip, 'rejected'));

                return ApiResponse::success(['status' => 'rejected'], 'All drivers rejected', 200);
            }

            // Case 3: Drivers found but no response
            if (! empty($allContactedDrivers) && $finalTrip->status->value === TripStatus::dispatched->value) {
                // Update trip status FIRST before sending any notifications
                $finalTrip->update(['status' => TripStatus::timeout->value]);

                // Set a flag to force terminate any other search processes
                Cache::put("trip_{$tripId}_force_terminate", true, 300);

                Log::info('Search timeout - No response from contacted drivers', [
                    'contacted_drivers' => count($allContactedDrivers),
                    'refused_drivers' => count($refusedDrivers ?? []),
                ]);

                broadcast(new SearchResponse($finalTrip, 'timeout'));

                foreach ($allContactedDrivers as $driverId) {
                    broadcast(new TripTimeoutOccurred($finalTrip, 'timeout', $driverId));
                }

                return ApiResponse::success(['status' => 'timeout'], 'No response from drivers', 200);
            }
        }

        Log::info('Search completed', [
            'total_drivers_contacted' => count($allContactedDrivers),
        ]);
        // clear the refused drivers in the database
        TripRefusedDriver::where('trip_id', $tripId)->delete();

        return ApiResponse::success(['status' => 'search_completed'], 'Search Completed', 200);
    }

    public function getValidDrivers($validDrivers, $vehicleCategory, string $vehicleType, bool $female_only = false)
    {
        $Drivers = [];
        foreach ($validDrivers as $driver) {
            // Use whereNull('deleted_at') to exclude soft-deleted drivers
            $driver = Driver::whereNull('deleted_at')
                ->where('id', $driver->id)
                ->first();

            // Skip if driver not found
            if (! $driver || ! $driver->user) {
                continue;
            }

            $status = $driver->user->status->value;
            if ($female_only === true && $driver->user->gender !== GenderEnum::female) {
                continue;
            }

            $driver_vehicle = $driver->vehicles()->where('status', UserStatus::ONLINE->value)->first();
            if ($driver_vehicle) {
                $category = $driver_vehicle->vehicleType->category->value;
                $vehicle_type = $driver_vehicle->vehicleType->name_en;

                if ($status === 'online' && $category === $vehicleCategory && $vehicle_type === $vehicleType) {
                    $Drivers[] = $driver->id;
                }
            }
        }

        return $Drivers;
    }

    public function filterValidDrivers(array $driverIds, $validated, int $tripId): array
    {
        $trip = Trip::where('id', $tripId)->first();

        if (! $trip) {
            return [];
        }

        $category = $trip->vehicleType->vehicle_category;
        $drivers = [];
        if ($category === 'passenger') {
            // Get the preferred seat number
            $preferredSeat = $trip->vehicleType->seat_number;

            $seatFallbackSequence = $this->getSeatFallbackSequence($preferredSeat);

            // Try each seat number in the fallback sequence
            foreach ($seatFallbackSequence as $currentSeatNumber) {

                foreach ($driverIds as $driverId) {
                    $driver = Driver::find($driverId);
                    if (! $driver) {
                        continue;
                    }

                    $driverVehicle = $driver->vehicles()
                        ->where('status', UserStatus::ONLINE->value)
                        ->first();

                    Log::info('this is the driver vehicle :', [$driverVehicle]);

                    if (! $driverVehicle || $driverVehicle->seat_number !== $currentSeatNumber) {
                        continue;
                    }

                    $hasEquipmentRequirements = $trip->vehicleType &&
                        $trip->vehicleType->vehicle_equipments !== null &&
                        $trip->vehicleType->vehicle_equipments !== '';

                    Log::info('Equipment requirements present:', [$hasEquipmentRequirements]);

                    // Parse required equipment if any
                    $requiredEquipments = $hasEquipmentRequirements
                        ? explode(',', $trip->vehicleType->vehicle_equipments)
                        : [];

                    // Get all the driver's equipment IDs
                    $driverEquipments = $driverVehicle->vehicleEquipments->pluck('id')->toArray();

                    // Check if the driver has all required equipment
                    $hasAllEquipments = empty($requiredEquipments) || empty(array_diff($requiredEquipments, $driverEquipments));

                    Log::info('Driver matches equipment requirements:', [$hasAllEquipments]);
                    if ($hasAllEquipments) {
                        $drivers[] = $driverId;

                        // Log if using fallback seat configuration
                        if ($currentSeatNumber !== $preferredSeat) {
                            Log::info('Seat fallback occurred', [
                                'trip_id' => $trip->id,
                                'preferred_seats' => $preferredSeat,
                                'actual_seats' => $currentSeatNumber,
                            ]);
                        }
                    }
                }
            }
        } elseif ($category === 'freight') {
            foreach ($driverIds as $driverId) {
                $driver = Driver::find($driverId);
                if (! $driver) {
                    continue;
                }

                $driverVehicle = $driver->vehicles()
                    ->where('status', UserStatus::ONLINE->value)
                    ->first();

                if (! $driverVehicle) {
                    continue;
                }

                // For freight vehicles, only check weight category and covered status
                if ($trip->vehicleType->weight_category === $driverVehicle->vehicleType->weight_category->value &&
                    $trip->vehicleType->is_covered === $driverVehicle->vehicleType->is_covered) {
                    $drivers[] = $driverId;
                }
            }
        }

        return $drivers;
    }

    public function getSeatFallbackSequence(int $preferredSeat): array
    {
        $fallbackMap = [
            2 => [2, 4, 6],
            4 => [4, 6],
            6 => [6],
        ];

        return $fallbackMap[$preferredSeat] ?? [];
    }

    public function nearBydrivers($lat, $lng, $distance)
    {
        return DB::select(
            'SELECT *,
                ST_Distance(
                    location,
                    ST_MakePoint(:lng, :lat)::geography
                ) as distance
             FROM drivers
             WHERE ST_DWithin(
                 location,
                 ST_MakePoint(:lng, :lat)::geography,
                 :distance
             )
             ORDER BY ST_Distance(
                 location,
                 ST_MakePoint(:lng, :lat)::geography
             ) ASC',
            [
                'lng' => $lng,
                'lat' => $lat,
                'distance' => $distance,
            ]
        );
    }

    /**
     * Get trip status
     *
     * @param  int  $id  Trip ID
     *
     * @response array{data : TripResource , message: string, status: integer,error : string}
     */
    public function Tripstatus($id)
    {
        $trip = Trip::find($id);
        if (! $trip) {
            return ApiResponse::error(null, 'Trip not found', 404);
        }

        $user = Auth::user();
        $isRider = $trip->rider && $trip->rider->user_id == $user->id;
        $isDriver = $trip->driver && $trip->driver->user_id == $user->id;

        if (! $isRider && ! $isDriver) {
            return ApiResponse::error(null, 'Unauthorized access', 401);
        }

        if ($trip->vehicle_id && $trip->driver_id) {
            $trip->load(['vehicle', 'driver.user']);
        }

        // Load rider and trip location
        $trip->load(['rider.user', 'tripLocation', 'vehicleType']);

        // Format image URLs with proper APP_URL and storage path
        if ($trip->driver && $trip->driver->user && $trip->driver->user->cover_picture) {
            $trip->driver->user->cover_picture = env('APP_URL', '/').'/storage/'.$trip->driver->user->cover_picture;
        } elseif ($trip->driver && $trip->driver->user) {
            $trip->driver->user->cover_picture = env('APP_URL', '/').'/images/avatar.png';
        }

        if ($trip->rider && $trip->rider->user && $trip->rider->user->cover_picture) {
            $trip->rider->user->cover_picture = env('APP_URL', '/').'/storage/'.$trip->rider->user->cover_picture;
        } elseif ($trip->rider && $trip->rider->user) {
            $trip->rider->user->cover_picture = env('APP_URL', '/').'/images/avatar.png';
        }

        if ($trip->vehicle && $trip->vehicle->image) {
            $trip->vehicle->image = env('APP_URL', '/').'/storage/'.$trip->vehicle->image;
        } elseif ($trip->vehicle) {
            $trip->vehicle->image = env('APP_URL', '/').'/images/vehicle.jpg';
        }

        // Safely decode pricing breakdown
        if ($trip->pricing_breakdown) {
            $decodedPricing = json_decode($trip->pricing_breakdown, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $trip->pricing_breakdown = $decodedPricing;
            } else {
                $trip->pricing_breakdown = [];
            }
        } else {
            $trip->pricing_breakdown = [];
        }

        return ApiResponse::success(
            $trip,
            'Trip status retrieved successfully',
            200
        );
    }

    /**
     * Display the specified trip
     */
    public function show(Trip $trip): ApiResponse
    {
        // Load all necessary relationships
        $trip->load([
            'rider.user',
            'driver.user',
            'vehicle.vehicleModel.vehicleBrand',
            'tripLocation',
            'vehicleType',
            'tripRatings',
        ]);

        // Format image URLs
        if ($trip->driver && $trip->driver->user && $trip->driver->user->cover_picture) {
            $trip->driver->user->cover_picture = env('APP_URL', '/').'/storage/'.$trip->driver->user->cover_picture;
        } elseif ($trip->driver && $trip->driver->user) {
            $trip->driver->user->cover_picture = env('APP_URL', '/').'/images/avatar.png';
        }

        if ($trip->rider && $trip->rider->user && $trip->rider->user->cover_picture) {
            $trip->rider->user->cover_picture = env('APP_URL', '/').'/storage/'.$trip->rider->user->cover_picture;
        } elseif ($trip->rider && $trip->rider->user) {
            $trip->rider->user->cover_picture = env('APP_URL', '/').'/images/avatar.png';
        }

        if ($trip->vehicle && $trip->vehicle->image) {
            $trip->vehicle->image = env('APP_URL', '/').'/storage/'.$trip->vehicle->image;
        } elseif ($trip->vehicle) {
            $trip->vehicle->image = env('APP_URL', '/').'/images/vehicle.jpg';
        }

        // Safely decode pricing breakdown
        if ($trip->pricing_breakdown) {
            $decodedPricing = json_decode($trip->pricing_breakdown, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $trip->pricing_breakdown = $decodedPricing;
            } else {
                $trip->pricing_breakdown = [];
            }
        } else {
            $trip->pricing_breakdown = [];
        }

        return ApiResponse::success(
            new TripResource($trip),
            'Trip retrieved successfully',
            200
        );
    }
}
