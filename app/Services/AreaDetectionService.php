<?php

namespace App\Services;

use App\Models\Area;
use Illuminate\Support\Facades\Cache;
use Throwable;

class AreaDetectionService
{
    private const CACHE_TTL = 3600;

    private const AREAS_CACHE_KEY = 'active_areas_with_polygons';

    private const EPSILON = 0.000001;

    /**
     * Retrieve the area ID by coordinates from cache or database.
     *
     * @param  float  $latitude
     * @param  float  $longitude
     * @return int|null
     */
    public function getAreaIdFromCoordinates(float $latitude, float $longitude): ?int
    {
        $cacheKey = 'area_coord_'.round($latitude, 6).'_'.round($longitude, 6);

        if (Cache::has($cacheKey)) {
            // Read from cache
            $cachedResult = Cache::get($cacheKey);

            // Ensure '0' (string) and 0 (int) are both treated as null
            return ((int) $cachedResult === 0) ? null : (int) $cachedResult;
        }

        // Find area ID from database
        $areaId = $this->findAreaIdByCoordinates($latitude, $longitude);

        // Cache null as 0 for distinction
        Cache::put($cacheKey, $areaId ?? 0, self::CACHE_TTL);

        return $areaId;
    }

    /**
     * Find the area ID that the given coordinates belong to.
     *
     * @param  float  $latitude
     * @param  float  $longitude
     * @return int|null
     */
    private function findAreaIdByCoordinates(float $latitude, float $longitude): ?int
    {
        try {
            $areas = $this->getActiveAreas();

            foreach ($areas as $area) {
                if ($this->pointInPolygon($latitude, $longitude, $area['polygon'])) {
                    return $area['id'];
                }
            }

            return null;
        } catch (Throwable $e) {
            return null;
        }
    }

    /**
     * Returns an array of active areas with their polygons.
     *
     * The result is cached for a short period of time to avoid hitting the database
     * on every request.
     *
     * @return array
     */
    private function getActiveAreas(): array
    {
        return Cache::remember(self::AREAS_CACHE_KEY, self::CACHE_TTL, function () {
            $areas = Area::select('id', 'polygon', 'name_en')
                ->where('is_active', true)
                ->get();

            $processedAreas = [];

            foreach ($areas as $area) {
                $polygon = is_string($area->polygon) ? json_decode($area->polygon, true) : $area->polygon;

                if (! is_array($polygon) || count($polygon) < 3) {
                    continue;
                }

                $validPolygon = true;
                foreach ($polygon as $point) {
                    if (! isset($point['lng'], $point['lat'])) {
                        $validPolygon = false;
                        break;
                    }
                }

                if ($validPolygon) {
                    $processedAreas[] = [
                        'id' => $area->id,
                        'polygon' => $polygon,
                    ];
                }
            }

            return $processedAreas;
        });
    }

    /**
     * Determines if a point is inside a polygon using the ray-casting algorithm.
     *
     * The algorithm works by casting a ray (a line extending from the point to the right)
     * and counting the number of times it intersects with the polygon's edges. If the
     * number of intersections is odd, the point is inside the polygon.
     *
     * @param float $latitude The latitude of the point to check
     * @param float $longitude The longitude of the point to check
     * @param array $polygon The polygon to check against, in the format:
     *     [
     *         ['lat' => float, 'lng' => float],
     *         ['lat' => float, 'lng' => float],
     *         ...
     *     ]
     *
     * @return bool True if the point is inside the polygon, false otherwise
     */
    private function pointInPolygon(float $latitude, float $longitude, array $polygon): bool
    {
        $inside = false;
        $n = count($polygon);

        for ($i = 0, $j = $n - 1; $i < $n; $j = $i++) {
            $xi = $polygon[$i]['lng'];
            $yi = $polygon[$i]['lat'];
            $xj = $polygon[$j]['lng'];
            $yj = $polygon[$j]['lat'];

            $yDiff = $yj - $yi;
            if (abs($yDiff) < self::EPSILON) {
                continue;
            }

            if ((($yi > $latitude) !== ($yj > $latitude)) &&
                ($longitude < ($xj - $xi) * ($latitude - $yi) / $yDiff + $xi)) {
                $inside = ! $inside;
            }
        }

        return $inside;
    }


    /**
     * Clear the cache of active areas and their polygons.
     *
     * This method is useful when an area is updated and the cache needs to be
     * invalidated.
     *
     * @return void
     */
    public function clearCache(): void
    {
        Cache::forget(self::AREAS_CACHE_KEY);
        // Optionally loop through known coordinate keys to forget them too
    }
}
