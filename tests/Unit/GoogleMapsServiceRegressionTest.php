<?php

namespace Tests\Unit;

use App\Services\GoogleMapsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GoogleMapsServiceRegressionTest extends TestCase
{
    use RefreshDatabase;

    private GoogleMapsService $service;
    private array $mockOrigin;
    private array $mockDestination;
    private array $mockGoogleResponse;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new GoogleMapsService();
        
        $this->mockOrigin = ['lat' => 32.8872, 'lng' => 13.1913];
        $this->mockDestination = ['lat' => 32.9042, 'lng' => 13.1856];
        
        $this->mockGoogleResponse = [
            'routes' => [
                [
                    'legs' => [
                        [
                            'distance' => [
                                'text' => '5.2 km',
                                'value' => 5200
                            ],
                            'duration' => [
                                'text' => '12 mins',
                                'value' => 720
                            ],
                            'duration_in_traffic' => [
                                'text' => '15 mins',
                                'value' => 900
                            ]
                        ]
                    ],
                    'overview_polyline' => [
                        'points' => 'test_polyline_encoded_string'
                    ]
                ]
            ],
            'status' => 'OK'
        ];
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }

    /** @test */
    public function backward_compatibility_all_old_methods_still_work()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Test all old methods still work exactly as before
        $distanceData = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        $distanceDataNoTraffic = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);
        $trafficData = $this->service->getDistanceAndTimeWithTraffic($this->mockOrigin, $this->mockDestination);
        $polyline = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination);
        $arrivalTime = $this->service->calculateEstimatedArrivalTime($this->mockOrigin, $this->mockDestination);

        // Verify all methods return expected data
        $this->assertNotNull($distanceData);
        $this->assertNotNull($distanceDataNoTraffic);
        $this->assertNotNull($trafficData);
        $this->assertNotNull($polyline);
        $this->assertNotNull($arrivalTime);

        // Verify data structure hasn't changed
        $this->assertArrayHasKey('distance_text', $distanceData);
        $this->assertArrayHasKey('distance_value', $distanceData);
        $this->assertArrayHasKey('duration_text', $distanceData);
        $this->assertArrayHasKey('duration_value', $distanceData);
    }

    /** @test */
    public function traffic_data_inclusion_is_now_correct()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Test with traffic enabled
        $withTraffic = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        
        // Test with traffic disabled
        $withoutTraffic = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);

        // With traffic should include traffic data
        $this->assertArrayHasKey('duration_in_traffic_text', $withTraffic);
        $this->assertArrayHasKey('duration_in_traffic_value', $withTraffic);

        // Without traffic should NOT include traffic data (this was the bug)
        $this->assertArrayNotHasKey('duration_in_traffic_text', $withoutTraffic);
        $this->assertArrayNotHasKey('duration_in_traffic_value', $withoutTraffic);
    }

    /** @test */
    public function cache_keys_are_now_properly_generated()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Clear cache first
        Cache::flush();

        // Make calls with different traffic settings
        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);

        // Verify correct cache keys exist (this was the bug - boolean conversion)
        $trafficCacheKey = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:1";
        $nonTrafficCacheKey = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:0";
        
        $this->assertTrue(Cache::has($trafficCacheKey), 'Traffic cache key should exist');
        $this->assertTrue(Cache::has($nonTrafficCacheKey), 'Non-traffic cache key should exist');
    }

    /** @test */
    public function new_complete_route_info_method_works()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Test the new optimized method
        $completeData = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, true);

        $this->assertNotNull($completeData);
        
        // Should have all the data in one response
        $this->assertArrayHasKey('distance_text', $completeData);
        $this->assertArrayHasKey('distance_value', $completeData);
        $this->assertArrayHasKey('duration_text', $completeData);
        $this->assertArrayHasKey('duration_value', $completeData);
        $this->assertArrayHasKey('polyline', $completeData);
        $this->assertArrayHasKey('duration_in_traffic_text', $completeData);
        $this->assertArrayHasKey('duration_in_traffic_value', $completeData);

        // Should make only one API call
        Http::assertSentCount(1);
    }

    /** @test */
    public function new_complete_route_info_respects_traffic_parameter()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Test with traffic disabled
        $completeDataNoTraffic = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, false);

        $this->assertNotNull($completeDataNoTraffic);
        
        // Should have basic data
        $this->assertArrayHasKey('distance_text', $completeDataNoTraffic);
        $this->assertArrayHasKey('distance_value', $completeDataNoTraffic);
        $this->assertArrayHasKey('duration_text', $completeDataNoTraffic);
        $this->assertArrayHasKey('duration_value', $completeDataNoTraffic);
        $this->assertArrayHasKey('polyline', $completeDataNoTraffic);
        
        // Should NOT have traffic data when traffic=false
        $this->assertArrayNotHasKey('duration_in_traffic_text', $completeDataNoTraffic);
        $this->assertArrayNotHasKey('duration_in_traffic_value', $completeDataNoTraffic);
    }

    /** @test */
    public function route_polyline_now_respects_traffic_parameter()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Clear cache
        Cache::flush();

        // Test default behavior (should be traffic=false now)
        $polyline1 = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination);
        
        // Test explicit traffic=true
        $polyline2 = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination, true);

        $this->assertEquals('test_polyline_encoded_string', $polyline1);
        $this->assertEquals('test_polyline_encoded_string', $polyline2);
        
        // Should make two API calls because traffic settings are different
        Http::assertSentCount(2);
    }

    /** @test */
    public function api_call_reduction_demonstration()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        Cache::flush();

        // OLD PATTERN: Separate calls (would make 2 API calls without caching)
        // $distanceData = $this->service->getDistanceAndTime($origin, $destination, false);
        // $polyline = $this->service->getRoutePolyline($origin, $destination, false);

        // NEW PATTERN: Single call
        $completeData = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, false);

        // Verify we get all the data we need
        $this->assertNotNull($completeData);
        $this->assertArrayHasKey('distance_value', $completeData);
        $this->assertArrayHasKey('polyline', $completeData);

        // Should make only 1 API call
        Http::assertSentCount(1);
    }

    /** @test */
    public function fallback_behavior_unchanged()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response(['status' => 'ZERO_RESULTS'], 200)
        ]);

        // Test that fallback behavior is the same for all methods
        $distanceData = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        $completeData = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, true);

        // Both should return fallback data
        $this->assertEquals('1 km', $distanceData['distance_text']);
        $this->assertEquals(1000, $distanceData['distance_value']);
        
        $this->assertEquals('1 km', $completeData['distance_text']);
        $this->assertEquals(1000, $completeData['distance_value']);
        $this->assertNotNull($completeData['polyline']); // Should have fallback polyline
    }

    /** @test */
    public function no_breaking_changes_in_response_format()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Get data using old method
        $oldMethodData = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        
        // Get data using new method
        $newMethodData = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, true);

        // Verify that all fields from old method are present in new method
        foreach ($oldMethodData as $key => $value) {
            $this->assertArrayHasKey($key, $newMethodData, "Key '$key' should be present in new method response");
            $this->assertEquals($value, $newMethodData[$key], "Value for key '$key' should be the same");
        }

        // New method should have additional 'polyline' field
        $this->assertArrayHasKey('polyline', $newMethodData);
    }
}
